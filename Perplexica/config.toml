[GENERAL]
SIMILARITY_MEASURE = "cosine"
KEEP_ALIVE = "5m"

[MODELS.OPENAI]
API_KEY = ""

[MODELS.GROQ]
API_KEY = ""

[MODELS.ANTHROPIC]
API_KEY = ""

[MODELS.GEMINI]
API_KEY = "AIzaSyC3vjdRuvxx6BuXgYnmwTB7ujz5PdemYxc"

[MODELS.CUSTOM_OPENAI]
API_KEY = "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"
API_URL = "https://openrouter.ai/api/v1"
MODEL_NAME = "openai/gpt-3.5-turbo"

[MODELS.OLLAMA]
API_URL = ""

[MODELS.DEEPSEEK]
API_KEY = ""

[MODELS.AIMLAPI]
API_KEY = ""

[MODELS.LM_STUDIO]
API_URL = ""

[API_ENDPOINTS]
SEARXNG = "http://searxng:8080"
