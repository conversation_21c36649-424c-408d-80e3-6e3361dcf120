# 🤖 Perplexica MCP Server

A full-featured Model Context Protocol (MCP) server for Perplexica AI search engine.

## ✨ Features

### 🛠️ Tools
- **perplexica_search** - Web search with AI-powered answers and sources
- **perplexica_chat** - Conversational AI with web search capabilities  
- **perplexica_suggest** - Get search suggestions and related queries

### 📚 Resources
- **perplexica://config** - Server configuration and status
- **perplexica://models** - Available AI models
- **perplexica://focus-modes** - Search focus modes and descriptions

### 💡 Prompts
- **research_assistant** - Academic and professional research helper
- **code_helper** - Programming assistant with web search
- **news_analyst** - Current events and news analysis

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Running Perplexica instance on `localhost:3001`

### Installation
```bash
cd /home/<USER>/perplexica-mcp-server
npm install
```

### Usage

#### Standalone
```bash
node index.js
```

#### With Claude Desktop
Add to your <PERSON>kt<PERSON> config:
```json
{
  "mcpServers": {
    "perplexica": {
      "command": "node",
      "args": ["/home/<USER>/perplexica-mcp-server/index.js"],
      "env": {
        "PERPLEXICA_API_URL": "http://localhost:3001"
      }
    }
  }
}
```

#### With VS Code
Add to your VS Code settings:
```json
{
  "mcp.servers": {
    "perplexica": {
      "command": "node", 
      "args": ["/home/<USER>/perplexica-mcp-server/index.js"],
      "env": {
        "PERPLEXICA_API_URL": "http://localhost:3001"
      }
    }
  }
}
```

## 🧪 Testing

```bash
node test-mcp.js
```

## 🔧 Configuration

### Environment Variables
- `PERPLEXICA_API_URL` - Perplexica API endpoint (default: `http://localhost:3001`)

### Focus Modes
- `webSearch` - General web search (default)
- `academicSearch` - Academic papers and research
- `writingAssistant` - Writing help and content creation
- `wolframAlphaSearch` - Mathematical computations
- `youtubeSearch` - YouTube video search
- `redditSearch` - Reddit discussions

### Optimization Modes
- `speed` - Fast responses
- `balanced` - Balance speed and quality (default)
- `quality` - Best quality responses

## 📖 Examples

### Search Tool
```json
{
  "name": "perplexica_search",
  "arguments": {
    "query": "latest developments in quantum computing",
    "focusMode": "academicSearch",
    "optimizationMode": "quality"
  }
}
```

### Chat Tool
```json
{
  "name": "perplexica_chat",
  "arguments": {
    "message": "Explain machine learning to a beginner",
    "focusMode": "webSearch",
    "chatId": "learning-session-1"
  }
}
```

### Research Assistant Prompt
```json
{
  "name": "research_assistant",
  "arguments": {
    "topic": "climate change impacts on agriculture",
    "depth": "comprehensive"
  }
}
```

## 🔒 Security

- Only accessible from localhost
- No external API exposure
- Secure communication with Perplexica backend

## 🤝 Integration

This MCP server integrates with:
- Claude Desktop
- VS Code with MCP extension
- Any MCP-compatible client
- Custom applications via stdio

## 📋 API Reference

### Tools

#### perplexica_search
Search the web with AI-powered analysis.

**Parameters:**
- `query` (string, required) - Search query
- `focusMode` (string, optional) - Search focus mode
- `optimizationMode` (string, optional) - Optimization mode

**Returns:** Formatted search results with sources

#### perplexica_chat  
Conversational AI with web search capabilities.

**Parameters:**
- `message` (string, required) - Message to send
- `focusMode` (string, optional) - Chat focus mode
- `optimizationMode` (string, optional) - Optimization mode
- `chatId` (string, optional) - Chat session ID

**Returns:** AI response with web-searched context

#### perplexica_suggest
Get search suggestions for a query.

**Parameters:**
- `query` (string, required) - Query to get suggestions for
- `count` (number, optional) - Number of suggestions (1-10)

**Returns:** List of related search suggestions

### Resources

#### perplexica://config
Current server configuration and status.

#### perplexica://models  
Available AI models and their configurations.

#### perplexica://focus-modes
Detailed information about all focus modes.

### Prompts

#### research_assistant
Academic and professional research assistant.

**Arguments:**
- `topic` (string, required) - Research topic
- `depth` (string, optional) - Research depth level

#### code_helper
Programming assistant with web search.

**Arguments:**
- `language` (string, required) - Programming language
- `problem` (string, required) - Programming problem

## 🐛 Troubleshooting

### Common Issues

1. **Connection refused**
   - Ensure Perplexica is running on `localhost:3001`
   - Check `PERPLEXICA_API_URL` environment variable

2. **Module not found**
   - Run `npm install` to install dependencies
   - Ensure Node.js 18+ is installed

3. **MCP client not connecting**
   - Verify MCP server path in client config
   - Check server logs for errors

### Logs
Server logs are output to stderr and can be viewed in your MCP client or terminal.

## 📄 License

MIT License - see LICENSE file for details.
