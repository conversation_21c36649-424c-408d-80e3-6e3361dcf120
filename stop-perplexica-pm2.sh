#!/bin/bash

# 🛑 Perplexica PM2 Services Stop Script

echo "🛑 Stopping Perplexica Services managed by PM2..."

# 1. Stop PM2 processes
echo "🔄 Stopping PM2 processes..."
pm2 stop all

if [ $? -eq 0 ]; then
    echo "✅ PM2 processes stopped successfully"
else
    echo "⚠️  Warning: Issues stopping PM2 processes"
fi

# 2. Delete PM2 processes (optional - uncomment if you want to completely remove)
# echo "🗑️  Deleting PM2 processes..."
# pm2 delete all

# 3. Stop Docker containers
echo "🐳 Stopping Docker containers..."
cd /home/<USER>/Perplexica
docker compose down

if [ $? -eq 0 ]; then
    echo "✅ Docker containers stopped successfully"
else
    echo "⚠️  Warning: Issues stopping Docker containers"
fi

# 4. Display final status
echo ""
echo "📋 Final PM2 Status:"
pm2 status

echo ""
echo "✅ All Perplexica services have been stopped!"
echo ""
echo "🚀 To start services again: ./start-perplexica-pm2.sh"
echo "📊 To monitor services: pm2 monit"
echo "📄 To view logs: pm2 logs"
