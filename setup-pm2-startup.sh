#!/bin/bash

# 🔧 PM2 Startup Setup Script for Perplexica

echo "🔧 Setting up PM2 startup for Perplexica..."

# 1. Generate PM2 startup script
echo "📝 Generating PM2 startup script..."
pm2 startup systemd -u ubuntu --hp /home/<USER>

echo ""
echo "⚠️  IMPORTANT: You need to run the command shown above as root/sudo!"
echo "   Copy and paste the 'sudo env PATH=...' command that was displayed"
echo ""

# 2. Save current PM2 configuration
echo "💾 Saving PM2 configuration..."
pm2 save

# 3. Create systemd service for auto-start Docker
echo "🐳 Creating systemd service for Docker auto-start..."
sudo tee /etc/systemd/system/perplexica-docker.service > /dev/null <<EOF
[Unit]
Description=Perplexica Docker Containers
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/home/<USER>/Perplexica
ExecStart=/usr/bin/docker compose up -d
ExecStop=/usr/bin/docker compose down
TimeoutStartSec=0
User=ubuntu
Group=ubuntu

[Install]
WantedBy=multi-user.target
EOF

# 4. Enable the service
echo "🔄 Enabling Perplexica Docker service..."
sudo systemctl daemon-reload
sudo systemctl enable perplexica-docker.service

# 5. Create a comprehensive startup script
echo "📜 Creating comprehensive startup script..."
cat > /home/<USER>/perplexica-startup.sh << 'EOF'
#!/bin/bash

# Wait for system to be ready
sleep 10

# Start Docker containers
echo "🐳 Starting Docker containers..."
cd /home/<USER>/Perplexica
docker compose up -d

# Wait for Perplexica to be ready
echo "⏳ Waiting for Perplexica to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:3001 >/dev/null 2>&1; then
        echo "✅ Perplexica is ready!"
        break
    fi
    sleep 2
done

# Start PM2 processes
echo "🚀 Starting PM2 processes..."
cd /home/<USER>/perplexica
pm2 resurrect

echo "✅ Perplexica startup complete!"
EOF

chmod +x /home/<USER>/perplexica-startup.sh

# 6. Add to user's crontab for additional safety
echo "⏰ Adding to crontab for additional startup safety..."
(crontab -l 2>/dev/null; echo "@reboot /home/<USER>/perplexica-startup.sh >> /home/<USER>/startup.log 2>&1") | crontab -

echo ""
echo "✅ PM2 startup setup complete!"
echo ""
echo "📋 What was configured:"
echo "   🔄 PM2 startup script generated (needs sudo execution)"
echo "   💾 PM2 configuration saved"
echo "   🐳 Docker systemd service created and enabled"
echo "   📜 Comprehensive startup script created"
echo "   ⏰ Crontab entry added for additional safety"
echo ""
echo "🔧 Next steps:"
echo "   1. Run the 'sudo env PATH=...' command shown above"
echo "   2. Test with: sudo systemctl start perplexica-docker"
echo "   3. Reboot to test full startup: sudo reboot"
echo ""
echo "📊 Monitor startup: tail -f /home/<USER>/startup.log"
