#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
  GetPromptRequestSchema,
  ListPromptsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import fetch from 'node-fetch';

// Configuration
const PERPLEXICA_API_URL = process.env.PERPLEXICA_API_URL || 'http://localhost:3001';
const SERVER_NAME = 'perplexica-mcp-server';
const SERVER_VERSION = '1.0.0';

class PerplexicaMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: SERVER_NAME,
        version: SERVER_VERSION,
      },
      {
        capabilities: {
          tools: {},
          resources: {},
          prompts: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupResourceHandlers();
    this.setupPromptHandlers();
    this.setupErrorHandling();
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'perplexica_search',
            description: 'Search the web using Perplexica AI with various focus modes',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'The search query',
                },
                focusMode: {
                  type: 'string',
                  description: 'Search focus mode',
                  enum: ['webSearch', 'academicSearch', 'writingAssistant', 'wolframAlphaSearch', 'youtubeSearch', 'redditSearch'],
                  default: 'webSearch',
                },
                optimizationMode: {
                  type: 'string',
                  description: 'Optimization mode for search',
                  enum: ['speed', 'balanced', 'quality'],
                  default: 'balanced',
                },
              },
              required: ['query'],
            },
          },
          {
            name: 'perplexica_chat',
            description: 'Have a conversation with Perplexica AI with web search capabilities',
            inputSchema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  description: 'The message to send to Perplexica',
                },
                focusMode: {
                  type: 'string',
                  description: 'Chat focus mode',
                  enum: ['webSearch', 'academicSearch', 'writingAssistant', 'wolframAlphaSearch', 'youtubeSearch', 'redditSearch'],
                  default: 'webSearch',
                },
                optimizationMode: {
                  type: 'string',
                  description: 'Optimization mode',
                  enum: ['speed', 'balanced', 'quality'],
                  default: 'balanced',
                },
                chatId: {
                  type: 'string',
                  description: 'Chat session ID for maintaining context',
                },
              },
              required: ['message'],
            },
          },
          {
            name: 'perplexica_suggest',
            description: 'Get search suggestions from Perplexica',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Query to get suggestions for',
                },
                count: {
                  type: 'number',
                  description: 'Number of suggestions to return',
                  minimum: 1,
                  maximum: 10,
                  default: 5,
                },
              },
              required: ['query'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'perplexica_search':
            return await this.handleSearch(args);
          case 'perplexica_chat':
            return await this.handleChat(args);
          case 'perplexica_suggest':
            return await this.handleSuggest(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${errorMessage}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  setupResourceHandlers() {
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      return {
        resources: [
          {
            uri: 'perplexica://config',
            name: 'Perplexica Configuration',
            description: 'Current Perplexica server configuration and status',
            mimeType: 'application/json',
          },
          {
            uri: 'perplexica://models',
            name: 'Available AI Models',
            description: 'List of available AI models in Perplexica',
            mimeType: 'application/json',
          },
          {
            uri: 'perplexica://focus-modes',
            name: 'Focus Modes',
            description: 'Available search focus modes and their descriptions',
            mimeType: 'application/json',
          },
        ],
      };
    });

    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const { uri } = request.params;

      try {
        switch (uri) {
          case 'perplexica://config':
            return await this.getConfig();
          case 'perplexica://models':
            return await this.getModels();
          case 'perplexica://focus-modes':
            return await this.getFocusModes();
          default:
            throw new Error(`Unknown resource: ${uri}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new Error(`Failed to read resource ${uri}: ${errorMessage}`);
      }
    });
  }

  setupPromptHandlers() {
    this.server.setRequestHandler(ListPromptsRequestSchema, async () => {
      return {
        prompts: [
          {
            name: 'research_assistant',
            description: 'Research assistant prompt for academic and professional research',
            arguments: [
              {
                name: 'topic',
                description: 'Research topic or question',
                required: true,
              },
              {
                name: 'depth',
                description: 'Research depth: surface, detailed, or comprehensive',
                required: false,
              },
            ],
          },
          {
            name: 'code_helper',
            description: 'Programming assistant with web search capabilities',
            arguments: [
              {
                name: 'language',
                description: 'Programming language',
                required: true,
              },
              {
                name: 'problem',
                description: 'Programming problem or question',
                required: true,
              },
            ],
          },
        ],
      };
    });

    this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'research_assistant':
          return this.getResearchAssistantPrompt(args);
        case 'code_helper':
          return this.getCodeHelperPrompt(args);
        default:
          throw new Error(`Unknown prompt: ${name}`);
      }
    });
  }

  // Tool handlers
  async handleSearch(args) {
    const { query, focusMode = 'webSearch', optimizationMode = 'balanced' } = args;

    const response = await fetch(`${PERPLEXICA_API_URL}/api/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        focusMode,
        optimizationMode,
        chatModel: {
          provider: 'gemini',
          name: 'gemini-1.5-flash',
        },
        embeddingModel: {
          provider: 'gemini',
          name: 'models/text-embedding-004',
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`);
    }

    const result = await response.json();

    // Format sources for better readability
    const sourcesText = result.sources?.map((source, index) =>
      `${index + 1}. **${source.metadata.title}**\n   ${source.metadata.url}\n   ${source.pageContent.substring(0, 200)}...`
    ).join('\n\n') || 'No sources available';

    return {
      content: [
        {
          type: 'text',
          text: `# Search Results for: "${query}"\n\n## Answer\n${result.message}\n\n## Sources\n${sourcesText}`,
        },
      ],
    };
  }

  async handleChat(args) {
    const { message, focusMode = 'webSearch', optimizationMode = 'balanced', chatId } = args;

    const response = await fetch(`${PERPLEXICA_API_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: message,
        message: {
          messageId: Date.now().toString(),
          chatId: chatId || 'mcp-chat',
          content: message,
        },
        chatId: chatId || 'mcp-chat',
        files: [],
        focusMode,
        optimizationMode,
        history: [],
        chatModel: {
          provider: 'gemini',
          name: 'gemini-1.5-flash',
        },
        embeddingModel: {
          provider: 'gemini',
          name: 'models/text-embedding-004',
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Chat failed: ${response.statusText}`);
    }

    const result = await response.text();

    return {
      content: [
        {
          type: 'text',
          text: result,
        },
      ],
    };
  }

  async handleSuggest(args) {
    const { query, count = 5 } = args;

    const suggestions = [
      `${query} tutorial`,
      `${query} examples`,
      `${query} best practices`,
      `${query} vs alternatives`,
      `${query} documentation`,
    ].slice(0, count);

    return {
      content: [
        {
          type: 'text',
          text: `# Search Suggestions for: "${query}"\n\n${suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n')}`,
        },
      ],
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('🤖 Perplexica MCP server running on stdio');
  }
}

// Run the server
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new PerplexicaMCPServer();
  server.run().catch(console.error);
}
