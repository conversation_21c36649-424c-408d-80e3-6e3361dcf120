#!/usr/bin/env node

const http = require('http');
const { exec } = require('child_process');

// Configuration
const HEALTH_CHECKS = [
  {
    name: 'Perplexica App',
    url: 'http://localhost:3001',
    timeout: 5000,
    critical: true
  },
  {
    name: 'Auth Proxy',
    url: 'http://localhost:8090',
    timeout: 5000,
    critical: true
  },
  {
    name: 'MCP Server',
    url: 'http://127.0.0.1:3004/health',
    timeout: 5000,
    critical: false
  }
];

const CHECK_INTERVAL = 30000; // 30 seconds
const MAX_FAILURES = 3;
const failures = {};

// Health check function
function checkHealth(service) {
  return new Promise((resolve) => {
    const req = http.get(service.url, { timeout: service.timeout }, (res) => {
      resolve({ success: true, status: res.statusCode });
    });

    req.on('error', (error) => {
      resolve({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });
  });
}

// Restart service function
function restartService(serviceName) {
  console.log(`🔄 Attempting to restart ${serviceName}...`);
  
  const restartCommands = {
    'Perplexica App': 'cd /home/<USER>/Perplexica && docker compose restart',
    'Auth Proxy': 'pm2 restart auth-proxy',
    'MCP Server': 'pm2 restart perplexica-mcp-server'
  };

  const command = restartCommands[serviceName];
  if (!command) {
    console.log(`❌ No restart command found for ${serviceName}`);
    return;
  }

  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.log(`❌ Failed to restart ${serviceName}: ${error.message}`);
    } else {
      console.log(`✅ ${serviceName} restarted successfully`);
      // Reset failure count on successful restart
      failures[serviceName] = 0;
    }
  });
}

// Main health check loop
async function performHealthChecks() {
  console.log(`🏥 Performing health checks at ${new Date().toISOString()}`);
  
  for (const service of HEALTH_CHECKS) {
    const result = await checkHealth(service);
    
    if (result.success) {
      console.log(`✅ ${service.name}: HEALTHY (${result.status})`);
      failures[service.name] = 0; // Reset failure count
    } else {
      failures[service.name] = (failures[service.name] || 0) + 1;
      console.log(`❌ ${service.name}: UNHEALTHY (${result.error}) - Failures: ${failures[service.name]}/${MAX_FAILURES}`);
      
      // Auto-restart if max failures reached and service is critical
      if (failures[service.name] >= MAX_FAILURES && service.critical) {
        console.log(`🚨 ${service.name} has failed ${MAX_FAILURES} times, attempting restart...`);
        restartService(service.name);
        failures[service.name] = 0; // Reset after restart attempt
      }
    }
  }
  
  console.log('---');
}

// Start health monitoring
console.log('🏥 Starting Perplexica Health Monitor...');
console.log(`⏰ Check interval: ${CHECK_INTERVAL / 1000} seconds`);
console.log(`🔄 Max failures before restart: ${MAX_FAILURES}`);
console.log('---');

// Initial check
performHealthChecks();

// Schedule regular checks
setInterval(performHealthChecks, CHECK_INTERVAL);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Health monitor shutting down...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Health monitor shutting down...');
  process.exit(0);
});
