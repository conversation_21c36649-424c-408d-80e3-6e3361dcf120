#!/bin/bash

# 🚀 Perplexica Services Startup Script

echo "🔄 Starting Perplexica Services..."

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start after $max_attempts attempts"
    return 1
}

# 1. Start Docker containers
echo "🐳 Starting Docker containers..."
cd /home/<USER>/Perplexica
docker compose up -d

if [ $? -eq 0 ]; then
    echo "✅ Docker containers started successfully"
else
    echo "❌ Failed to start Docker containers"
    exit 1
fi

# Wait for Perplexica app to be ready
wait_for_service "http://localhost:3001" "Perplexica App"

# 2. Start Auth Proxy
echo "🔐 Starting Auth Proxy..."
cd /home/<USER>

# Kill existing auth proxy if running
pkill -f simple-auth-proxy.js >/dev/null 2>&1

# Start auth proxy in background
nohup node simple-auth-proxy.js > auth-proxy.log 2>&1 &
AUTH_PROXY_PID=$!

# Wait for auth proxy to be ready
wait_for_service "http://localhost:8090" "Auth Proxy"

# 3. Start API Server
echo "🤖 Starting API Server..."

# Kill existing API server if running
pkill -f perplexica-mcp-server.js >/dev/null 2>&1

# Start API server in background
nohup node perplexica-mcp-server.js > api-server.log 2>&1 &
API_SERVER_PID=$!

# Wait for API server to be ready
wait_for_service "http://127.0.0.1:3004/health" "API Server"

# 4. Display status
echo ""
echo "🎉 All services started successfully!"
echo ""
echo "📋 Service Status:"
echo "   🐳 Docker Containers: $(docker compose ps --format 'table {{.Service}}\t{{.Status}}' | grep -c 'Up')/2 running"
echo "   🔐 Auth Proxy: http://localhost:8090 (PID: $AUTH_PROXY_PID)"
echo "   🤖 API Server: http://127.0.0.1:3004 (PID: $API_SERVER_PID)"
echo "   📡 Internal API: http://localhost:3001"
echo ""
echo "🔑 Auth Credentials:"
echo "   Username: tomkho12"
echo "   Password: Twilv0zera@123"
echo ""
echo "📚 Documentation: /home/<USER>/perplexica-api-guide.md"
echo ""
echo "🛑 To stop all services: ./stop-perplexica-services.sh"
