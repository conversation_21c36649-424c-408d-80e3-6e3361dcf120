# 🤖 Perplexica Internal API Guide

## 📋 Available Endpoints

### 1. 🔍 **Direct API Access (No Auth Required)**
- **URL**: `http://localhost:3001`
- **Usage**: Direct access for internal VPS applications
- **Security**: Only accessible from localhost

### 2. 🛡️ **Web Access (Auth Required)**
- **URL**: `http://localhost:8090`
- **Username**: `tomkho12`
- **Password**: `Twilv0zera@123`
- **Usage**: Web browser access with authentication

### 3. 🤖 **Simplified API Server**
- **URL**: `http://127.0.0.1:3004`
- **Usage**: Simplified REST API for easy integration
- **Security**: Only accessible from localhost

## 🚀 API Usage Examples

### Direct Perplexica API (localhost:3001)

#### Search API
```bash
curl -X POST http://localhost:3001/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is artificial intelligence?",
    "focusMode": "webSearch",
    "optimizationMode": "balanced",
    "chatModel": {
      "provider": "gemini",
      "name": "gemini-1.5-flash"
    },
    "embeddingModel": {
      "provider": "gemini",
      "name": "models/text-embedding-004"
    }
  }'
```

#### Chat API
```bash
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Explain quantum computing",
    "message": {
      "messageId": "msg123",
      "chatId": "chat456",
      "content": "Explain quantum computing"
    },
    "chatId": "chat456",
    "files": [],
    "focusMode": "webSearch",
    "optimizationMode": "balanced",
    "history": [],
    "chatModel": {
      "provider": "gemini",
      "name": "gemini-1.5-flash"
    },
    "embeddingModel": {
      "provider": "gemini",
      "name": "models/text-embedding-004"
    }
  }'
```

### Simplified API Server (127.0.0.1:3004)

#### Search
```bash
curl -X POST http://127.0.0.1:3004/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is machine learning?",
    "focusMode": "webSearch",
    "optimizationMode": "balanced"
  }'
```

#### Chat
```bash
curl -X POST http://127.0.0.1:3004/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Tell me about blockchain technology",
    "focusMode": "webSearch",
    "optimizationMode": "balanced"
  }'
```

#### Health Check
```bash
curl http://127.0.0.1:3004/health
```

## 🔧 Focus Modes

- `webSearch` - General web search (default)
- `academicSearch` - Academic papers and research
- `writingAssistant` - Writing help and suggestions
- `wolframAlphaSearch` - Mathematical computations
- `youtubeSearch` - YouTube video search
- `redditSearch` - Reddit discussions

## ⚡ Optimization Modes

- `speed` - Fast responses
- `balanced` - Balance between speed and quality (default)
- `quality` - Best quality responses

## 🐍 Python Example

```python
import requests
import json

def search_perplexica(query, focus_mode="webSearch"):
    url = "http://127.0.0.1:3004/search"
    data = {
        "query": query,
        "focusMode": focus_mode,
        "optimizationMode": "balanced"
    }
    
    response = requests.post(url, json=data)
    return response.json()

# Usage
result = search_perplexica("What is Docker?")
print(result["message"])
```

## 🟢 Node.js Example

```javascript
const fetch = require('node-fetch');

async function chatWithPerplexica(message) {
  const response = await fetch('http://127.0.0.1:3004/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: message,
      focusMode: 'webSearch',
      optimizationMode: 'balanced'
    })
  });
  
  return await response.text();
}

// Usage
chatWithPerplexica("Explain Kubernetes").then(console.log);
```

## 🔒 Security Notes

- Port 3001: Only accessible from localhost (127.0.0.1)
- Port 3004: Only accessible from localhost (127.0.0.1)
- Port 8090: Requires authentication for external access
- No external ports exposed for direct API access

## 🚀 Service Management

### Start Services
```bash
# Start Docker containers
cd /home/<USER>/Perplexica
docker compose up -d

# Start auth proxy
cd /home/<USER>

# Start API server
cd /home/<USER>
```

### Stop Services
```bash
# Stop Docker containers
cd /home/<USER>/Perplexica
docker compose down

# Kill proxy and API server
pkill -f simple-auth-proxy.js
pkill -f perplexica-mcp-server.js
```
