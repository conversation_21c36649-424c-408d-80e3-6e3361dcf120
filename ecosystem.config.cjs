module.exports = {
  apps: [
    {
      name: 'perplexica-mcp-server',
      script: 'index.js',
      cwd: '/home/<USER>/perplexica',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PERPLEXICA_API_URL: 'http://localhost:3001'
      },
      error_file: './logs/mcp-server-error.log',
      out_file: './logs/mcp-server-out.log',
      log_file: './logs/mcp-server-combined.log',
      time: true,
      // Restart strategies
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      // Health check
      health_check_grace_period: 3000,
      // Advanced restart options
      exponential_backoff_restart_delay: 100,
      kill_timeout: 5000,
      listen_timeout: 3000,
      // Monitoring
      pmx: true,
      merge_logs: true
    },
    {
      name: 'auth-proxy',
      script: 'simple-auth-proxy.js',
      cwd: '/home/<USER>/perplexica',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      env: {
        NODE_ENV: 'production'
      },
      error_file: './logs/auth-proxy-error.log',
      out_file: './logs/auth-proxy-out.log',
      log_file: './logs/auth-proxy-combined.log',
      time: true,
      // Restart strategies
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      // Health check
      health_check_grace_period: 3000,
      // Advanced restart options
      exponential_backoff_restart_delay: 100,
      kill_timeout: 5000,
      listen_timeout: 3000,
      // Monitoring
      pmx: true,
      merge_logs: true
    },
    {
      name: 'health-monitor',
      script: 'health-check.js',
      cwd: '/home/<USER>/perplexica',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '200M',
      env: {
        NODE_ENV: 'production'
      },
      error_file: './logs/health-monitor-error.log',
      out_file: './logs/health-monitor-out.log',
      log_file: './logs/health-monitor-combined.log',
      time: true,
      // Restart strategies
      min_uptime: '10s',
      max_restarts: 5,
      restart_delay: 10000,
      // Health check
      health_check_grace_period: 5000,
      // Advanced restart options
      exponential_backoff_restart_delay: 100,
      kill_timeout: 5000,
      listen_timeout: 3000,
      // Monitoring
      pmx: true,
      merge_logs: true
    }
  ],

  deploy: {
    production: {
      user: 'ubuntu',
      host: 'localhost',
      ref: 'origin/main',
      repo: '**************:repo.git',
      path: '/home/<USER>/perplexica',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
