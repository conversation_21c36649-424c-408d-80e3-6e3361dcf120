#!/bin/bash

# 🔄 Perplexica PM2 Services Restart Script

echo "🔄 Restarting Perplexica Services with PM2..."

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=15
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start after $max_attempts attempts"
    return 1
}

# 1. Restart PM2 processes
echo "🔄 Restarting PM2 processes..."
pm2 restart all

if [ $? -eq 0 ]; then
    echo "✅ PM2 processes restarted successfully"
else
    echo "❌ Failed to restart PM2 processes"
    exit 1
fi

# 2. Wait for services to be ready
sleep 5

# Wait for auth proxy to be ready
wait_for_service "http://localhost:8090" "Auth Proxy"

# Wait for MCP server to be ready
wait_for_service "http://127.0.0.1:3004/health" "MCP Server" || echo "⚠️  MCP Server health check failed, but service may still be running"

# 3. Display status
echo ""
echo "🎉 All services restarted successfully!"
echo ""
echo "📋 Current Status:"
pm2 status

echo ""
echo "📊 Monitoring Commands:"
echo "   📈 Monitor: pm2 monit"
echo "   📄 Logs: pm2 logs"
echo "   🔄 Restart specific: pm2 restart <app-name>"
echo "   🛑 Stop: pm2 stop all"
