#!/bin/bash

# 🛑 Perplexica Services Stop Script

echo "🛑 Stopping Perplexica Services..."

# 1. Stop Docker containers
echo "🐳 Stopping Docker containers..."
cd /home/<USER>/Perplexica
docker compose down

if [ $? -eq 0 ]; then
    echo "✅ Docker containers stopped successfully"
else
    echo "⚠️  Warning: Issues stopping Docker containers"
fi

# 2. Stop Auth Proxy
echo "🔐 Stopping Auth Proxy..."
pkill -f simple-auth-proxy.js
if [ $? -eq 0 ]; then
    echo "✅ Auth Proxy stopped"
else
    echo "ℹ️  Auth Proxy was not running"
fi

# 3. Stop API Server
echo "🤖 Stopping API Server..."
pkill -f perplexica-mcp-server.js
if [ $? -eq 0 ]; then
    echo "✅ API Server stopped"
else
    echo "ℹ️  API Server was not running"
fi

# 4. Clean up log files
echo "🧹 Cleaning up log files..."
cd /home/<USER>
rm -f auth-proxy.log api-server.log

echo ""
echo "✅ All Perplexica services have been stopped!"
echo ""
echo "🚀 To start services again: ./start-perplexica-services.sh"
