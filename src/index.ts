#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
  GetPromptRequestSchema,
  ListPromptsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import fetch from 'node-fetch';

// Configuration
const PERPLEXICA_API_URL = process.env.PERPLEXICA_API_URL || 'http://localhost:3001';
const SERVER_NAME = 'perplexica-mcp-server';
const SERVER_VERSION = '1.0.0';

// Validation schemas
const SearchArgsSchema = z.object({
  query: z.string().describe('The search query'),
  focusMode: z.enum(['webSearch', 'academicSearch', 'writingAssistant', 'wolframAlphaSearch', 'youtubeSearch', 'redditSearch']).default('webSearch'),
  optimizationMode: z.enum(['speed', 'balanced', 'quality']).default('balanced'),
});

const ChatArgsSchema = z.object({
  message: z.string().describe('The message to send'),
  focusMode: z.enum(['webSearch', 'academicSearch', 'writingAssistant', 'wolframAlphaSearch', 'youtubeSearch', 'redditSearch']).default('webSearch'),
  optimizationMode: z.enum(['speed', 'balanced', 'quality']).default('balanced'),
  chatId: z.string().optional().describe('Chat session ID'),
  history: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string()
  })).optional().describe('Chat history'),
});

const SuggestArgsSchema = z.object({
  query: z.string().describe('Query to get suggestions for'),
  count: z.number().min(1).max(10).default(5).describe('Number of suggestions'),
});

class PerplexicaMCPServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: SERVER_NAME,
        version: SERVER_VERSION,
      },
      {
        capabilities: {
          tools: {},
          resources: {},
          prompts: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupResourceHandlers();
    this.setupPromptHandlers();
    this.setupErrorHandling();
  }

  private setupErrorHandling(): void {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers(): void {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'perplexica_search',
            description: 'Search the web using Perplexica AI with various focus modes',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'The search query',
                },
                focusMode: {
                  type: 'string',
                  description: 'Search focus mode',
                  enum: ['webSearch', 'academicSearch', 'writingAssistant', 'wolframAlphaSearch', 'youtubeSearch', 'redditSearch'],
                  default: 'webSearch',
                },
                optimizationMode: {
                  type: 'string',
                  description: 'Optimization mode for search',
                  enum: ['speed', 'balanced', 'quality'],
                  default: 'balanced',
                },
              },
              required: ['query'],
            },
          },
          {
            name: 'perplexica_chat',
            description: 'Have a conversation with Perplexica AI with web search capabilities',
            inputSchema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  description: 'The message to send to Perplexica',
                },
                focusMode: {
                  type: 'string',
                  description: 'Chat focus mode',
                  enum: ['webSearch', 'academicSearch', 'writingAssistant', 'wolframAlphaSearch', 'youtubeSearch', 'redditSearch'],
                  default: 'webSearch',
                },
                optimizationMode: {
                  type: 'string',
                  description: 'Optimization mode',
                  enum: ['speed', 'balanced', 'quality'],
                  default: 'balanced',
                },
                chatId: {
                  type: 'string',
                  description: 'Chat session ID for maintaining context',
                },
                history: {
                  type: 'array',
                  description: 'Previous chat history',
                  items: {
                    type: 'object',
                    properties: {
                      role: { type: 'string', enum: ['user', 'assistant'] },
                      content: { type: 'string' }
                    },
                    required: ['role', 'content']
                  }
                }
              },
              required: ['message'],
            },
          }

  private setupResourceHandlers(): void {
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      return {
        resources: [
          {
            uri: 'perplexica://config',
            name: 'Perplexica Configuration',
            description: 'Current Perplexica server configuration and status',
            mimeType: 'application/json',
          },
          {
            uri: 'perplexica://models',
            name: 'Available AI Models',
            description: 'List of available AI models in Perplexica',
            mimeType: 'application/json',
          },
          {
            uri: 'perplexica://focus-modes',
            name: 'Focus Modes',
            description: 'Available search focus modes and their descriptions',
            mimeType: 'application/json',
          },
        ],
      };
    });

    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const { uri } = request.params;

      try {
        switch (uri) {
          case 'perplexica://config':
            return await this.getConfig();
          case 'perplexica://models':
            return await this.getModels();
          case 'perplexica://focus-modes':
            return await this.getFocusModes();
          default:
            throw new Error(`Unknown resource: ${uri}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new Error(`Failed to read resource ${uri}: ${errorMessage}`);
      }
    });
  }

  private setupPromptHandlers(): void {
    this.server.setRequestHandler(ListPromptsRequestSchema, async () => {
      return {
        prompts: [
          {
            name: 'research_assistant',
            description: 'Research assistant prompt for academic and professional research',
            arguments: [
              {
                name: 'topic',
                description: 'Research topic or question',
                required: true,
              },
              {
                name: 'depth',
                description: 'Research depth: surface, detailed, or comprehensive',
                required: false,
              },
            ],
          },
          {
            name: 'code_helper',
            description: 'Programming assistant with web search capabilities',
            arguments: [
              {
                name: 'language',
                description: 'Programming language',
                required: true,
              },
              {
                name: 'problem',
                description: 'Programming problem or question',
                required: true,
              },
            ],
          },
          {
            name: 'news_analyst',
            description: 'Current events and news analysis assistant',
            arguments: [
              {
                name: 'topic',
                description: 'News topic or event to analyze',
                required: true,
              },
              {
                name: 'timeframe',
                description: 'Time period: today, week, month',
                required: false,
              },
            ],
          },
        ],
      };
    });

    this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'research_assistant':
          return this.getResearchAssistantPrompt(args);
        case 'code_helper':
          return this.getCodeHelperPrompt(args);
        case 'news_analyst':
          return this.getNewsAnalystPrompt(args);
        default:
          throw new Error(`Unknown prompt: ${name}`);
      }
    });
  }

  // Tool handlers
  private async handleSearch(args: any) {
    const validatedArgs = SearchArgsSchema.parse(args);
    const { query, focusMode, optimizationMode } = validatedArgs;

    const response = await fetch(`${PERPLEXICA_API_URL}/api/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        focusMode,
        optimizationMode,
        chatModel: {
          provider: 'gemini',
          name: 'gemini-1.5-flash',
        },
        embeddingModel: {
          provider: 'gemini',
          name: 'models/text-embedding-004',
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`);
    }

    const result = await response.json();

    // Format sources for better readability
    const sourcesText = result.sources?.map((source: any, index: number) =>
      `${index + 1}. **${source.metadata.title}**\n   ${source.metadata.url}\n   ${source.pageContent.substring(0, 200)}...`
    ).join('\n\n') || 'No sources available';

    return {
      content: [
        {
          type: 'text',
          text: `# Search Results for: "${query}"\n\n## Answer\n${result.message}\n\n## Sources\n${sourcesText}`,
        },
      ],
    };
  }

  private async handleChat(args: any) {
    const validatedArgs = ChatArgsSchema.parse(args);
    const { message, focusMode, optimizationMode, chatId, history } = validatedArgs;

    const response = await fetch(`${PERPLEXICA_API_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: message,
        message: {
          messageId: Date.now().toString(),
          chatId: chatId || 'mcp-chat',
          content: message,
        },
        chatId: chatId || 'mcp-chat',
        files: [],
        focusMode,
        optimizationMode,
        history: history || [],
        chatModel: {
          provider: 'gemini',
          name: 'gemini-1.5-flash',
        },
        embeddingModel: {
          provider: 'gemini',
          name: 'models/text-embedding-004',
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Chat failed: ${response.statusText}`);
    }

    const result = await response.text();

    return {
      content: [
        {
          type: 'text',
          text: result,
        },
      ],
    };
  }

  private async handleSuggest(args: any) {
    const validatedArgs = SuggestArgsSchema.parse(args);
    const { query, count } = validatedArgs;

    const response = await fetch(`${PERPLEXICA_API_URL}/api/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        count,
      }),
    });

    if (!response.ok) {
      throw new Error(`Suggestions failed: ${response.statusText}`);
    }

    const suggestions = await response.json();

    return {
      content: [
        {
          type: 'text',
          text: `# Search Suggestions for: "${query}"\n\n${suggestions.map((s: string, i: number) => `${i + 1}. ${s}`).join('\n')}`,
        },
      ],
    };
  },
          {
            name: 'perplexica_suggest',
            description: 'Get search suggestions from Perplexica',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Query to get suggestions for',
                },
                count: {
                  type: 'number',
                  description: 'Number of suggestions to return',
                  minimum: 1,
                  maximum: 10,
                  default: 5,
                },
              },
              required: ['query'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'perplexica_search':
            return await this.handleSearch(args);
          case 'perplexica_chat':
            return await this.handleChat(args);
          case 'perplexica_suggest':
            return await this.handleSuggest(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${errorMessage}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  // Resource handlers
  private async getConfig() {
    try {
      const response = await fetch(`${PERPLEXICA_API_URL}/api/config`);
      const config = response.ok ? await response.json() : { status: 'unknown' };

      return {
        contents: [
          {
            uri: 'perplexica://config',
            mimeType: 'application/json',
            text: JSON.stringify({
              server_url: PERPLEXICA_API_URL,
              status: 'connected',
              config: config,
              timestamp: new Date().toISOString(),
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        contents: [
          {
            uri: 'perplexica://config',
            mimeType: 'application/json',
            text: JSON.stringify({
              server_url: PERPLEXICA_API_URL,
              status: 'error',
              error: error instanceof Error ? error.message : 'Unknown error',
              timestamp: new Date().toISOString(),
            }, null, 2),
          },
        ],
      };
    }
  }

  private async getModels() {
    try {
      const response = await fetch(`${PERPLEXICA_API_URL}/api/models`);
      const models = response.ok ? await response.json() : {};

      return {
        contents: [
          {
            uri: 'perplexica://models',
            mimeType: 'application/json',
            text: JSON.stringify({
              available_models: models,
              default_chat_model: {
                provider: 'gemini',
                name: 'gemini-1.5-flash',
              },
              default_embedding_model: {
                provider: 'gemini',
                name: 'models/text-embedding-004',
              },
              timestamp: new Date().toISOString(),
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        contents: [
          {
            uri: 'perplexica://models',
            mimeType: 'application/json',
            text: JSON.stringify({
              error: error instanceof Error ? error.message : 'Unknown error',
              timestamp: new Date().toISOString(),
            }, null, 2),
          },
        ],
      };
    }
  }

  private async getFocusModes() {
    const focusModes = {
      webSearch: {
        name: 'Web Search',
        description: 'General web search across multiple sources',
        use_cases: ['General questions', 'Current events', 'Product research'],
      },
      academicSearch: {
        name: 'Academic Search',
        description: 'Search academic papers and scholarly content',
        use_cases: ['Research papers', 'Scientific studies', 'Academic citations'],
      },
      writingAssistant: {
        name: 'Writing Assistant',
        description: 'Help with writing, editing, and content creation',
        use_cases: ['Content writing', 'Editing', 'Creative writing'],
      },
      wolframAlphaSearch: {
        name: 'Wolfram Alpha Search',
        description: 'Mathematical computations and data analysis',
        use_cases: ['Math problems', 'Data analysis', 'Scientific calculations'],
      },
      youtubeSearch: {
        name: 'YouTube Search',
        description: 'Search YouTube videos and content',
        use_cases: ['Video tutorials', 'Entertainment', 'Educational content'],
      },
      redditSearch: {
        name: 'Reddit Search',
        description: 'Search Reddit discussions and communities',
        use_cases: ['Community opinions', 'Discussions', 'User experiences'],
      },
    };

    return {
      contents: [
        {
          uri: 'perplexica://focus-modes',
          mimeType: 'application/json',
          text: JSON.stringify({
            focus_modes: focusModes,
            timestamp: new Date().toISOString(),
          }, null, 2),
        },
      ],
    };
  }

  // Prompt handlers
  private getResearchAssistantPrompt(args: any) {
    const { topic, depth = 'detailed' } = args;

    return {
      description: `Research assistant for ${topic}`,
      messages: [
        {
          role: 'user',
          content: {
            type: 'text',
            text: `You are a research assistant. Please conduct ${depth} research on: ${topic}

Please provide:
1. A comprehensive overview
2. Key findings and insights
3. Current trends and developments
4. Reliable sources and references
5. Practical implications

Focus on accuracy and cite your sources.`,
          },
        },
      ],
    };
  }

  private getCodeHelperPrompt(args: any) {
    const { language, problem } = args;

    return {
      description: `Programming assistant for ${language}`,
      messages: [
        {
          role: 'user',
          content: {
            type: 'text',
            text: `You are a programming assistant specializing in ${language}. Help me with: ${problem}

Please provide:
1. Clear explanation of the solution
2. Working code examples
3. Best practices and conventions
4. Common pitfalls to avoid
5. Additional resources for learning

Make sure the code is well-commented and follows ${language} best practices.`,
          },
        },
      ],
    };
  }

  private getNewsAnalystPrompt(args: any) {
    const { topic, timeframe = 'week' } = args;

    return {
      description: `News analysis for ${topic}`,
      messages: [
        {
          role: 'user',
          content: {
            type: 'text',
            text: `You are a news analyst. Analyze recent news about: ${topic} (timeframe: ${timeframe})

Please provide:
1. Summary of key developments
2. Analysis of trends and patterns
3. Different perspectives and viewpoints
4. Potential implications and consequences
5. Credible news sources

Focus on factual reporting and balanced analysis.`,
          },
        },
      ],
    };
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Perplexica MCP server running on stdio');
  }
}

// Run the server
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new PerplexicaMCPServer();
  server.run().catch(console.error);
}
