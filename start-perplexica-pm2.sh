#!/bin/bash

# 🚀 Perplexica PM2 Services Startup Script

echo "🔄 Starting Perplexica Services with PM2..."

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start after $max_attempts attempts"
    return 1
}

# 1. Start Docker containers first
echo "🐳 Starting Docker containers..."
cd /home/<USER>/Perplexica
docker compose up -d

if [ $? -eq 0 ]; then
    echo "✅ Docker containers started successfully"
else
    echo "❌ Failed to start Docker containers"
    exit 1
fi

# Wait for Perplexica app to be ready
wait_for_service "http://localhost:3001" "Perplexica App"

# 2. Build the MCP server if needed
echo "🔨 Building MCP Server..."
cd /home/<USER>/perplexica
if [ ! -d "dist" ] || [ ! -f "dist/index.js" ]; then
    echo "📦 Building TypeScript..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ Build failed"
        exit 1
    fi
fi

# 3. Stop any existing PM2 processes
echo "🛑 Stopping existing PM2 processes..."
pm2 delete all 2>/dev/null || true

# 4. Start services with PM2
echo "🚀 Starting services with PM2..."
cd /home/<USER>/perplexica
pm2 start ecosystem.config.cjs

if [ $? -eq 0 ]; then
    echo "✅ PM2 services started successfully"
else
    echo "❌ Failed to start PM2 services"
    exit 1
fi

# 5. Save PM2 configuration
echo "💾 Saving PM2 configuration..."
pm2 save

# 6. Setup PM2 startup script
echo "🔧 Setting up PM2 startup..."
pm2 startup systemd -u ubuntu --hp /home/<USER>/dev/null || true

# 7. Wait for services to be ready
sleep 5

# Wait for auth proxy to be ready
wait_for_service "http://localhost:8090" "Auth Proxy"

# Wait for MCP server to be ready (health check endpoint)
wait_for_service "http://127.0.0.1:3004/health" "MCP Server" || echo "⚠️  MCP Server health check failed, but service may still be running"

# 8. Display status
echo ""
echo "🎉 All services started successfully with PM2!"
echo ""
echo "📋 Service Status:"
pm2 status

echo ""
echo "📊 PM2 Monitoring:"
echo "   📈 Monitor: pm2 monit"
echo "   📋 Status: pm2 status"
echo "   📄 Logs: pm2 logs"
echo "   🔄 Restart: pm2 restart all"
echo "   🛑 Stop: pm2 stop all"
echo ""
echo "🔑 Auth Credentials:"
echo "   Username: tomkho12"
echo "   Password: Twilv0zera@123"
echo ""
echo "🌐 Service URLs:"
echo "   🔐 Auth Proxy: http://localhost:8090"
echo "   🤖 MCP Server: http://127.0.0.1:3004"
echo "   📡 Internal API: http://localhost:3001"
echo ""
echo "📚 Documentation: /home/<USER>/perplexica-api-guide.md"
echo ""
echo "🛑 To stop all services: ./stop-perplexica-pm2.sh"
