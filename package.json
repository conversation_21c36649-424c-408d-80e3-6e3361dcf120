{"name": "perplexica-mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for Perplexica AI search engine", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "node test/test-mcp.js"}, "keywords": ["mcp", "perplexica", "ai", "search", "model-context-protocol"], "author": "tomkho12", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "node-fetch": "^3.3.2", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}