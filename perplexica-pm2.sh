#!/bin/bash

# 🤖 Perplexica PM2 Management Script

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

show_help() {
    echo "🤖 Perplexica PM2 Management Script"
    echo "=================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     🚀 Start all Perplexica services with PM2"
    echo "  stop      🛑 Stop all Perplexica services"
    echo "  restart   🔄 Restart all Perplexica services"
    echo "  status    📋 Show status of all services"
    echo "  monitor   📊 Show monitoring dashboard"
    echo "  logs      📄 Show logs from all services"
    echo "  health    🏥 Show health check status"
    echo "  setup     🔧 Setup PM2 startup (run once)"
    echo "  help      ❓ Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start    # Start all services"
    echo "  $0 monitor  # Show monitoring dashboard"
    echo "  $0 logs     # Show live logs"
    echo ""
}

case "$1" in
    start)
        echo "🚀 Starting Perplexica services with PM2..."
        "$SCRIPT_DIR/start-perplexica-pm2.sh"
        ;;
    stop)
        echo "🛑 Stopping Perplexica services..."
        "$SCRIPT_DIR/stop-perplexica-pm2.sh"
        ;;
    restart)
        echo "🔄 Restarting Perplexica services..."
        "$SCRIPT_DIR/restart-perplexica-pm2.sh"
        ;;
    status)
        echo "📋 Perplexica Services Status"
        echo "============================"
        pm2 status
        echo ""
        echo "🐳 Docker Status:"
        cd /home/<USER>/Perplexica && docker compose ps
        ;;
    monitor)
        "$SCRIPT_DIR/monitor-perplexica.sh"
        ;;
    logs)
        echo "📄 Live logs from all services (Ctrl+C to exit):"
        pm2 logs
        ;;
    health)
        echo "🏥 Health Check Status:"
        echo "======================"
        
        # Check if health monitor is running
        if pm2 list | grep -q "health-monitor.*online"; then
            echo "✅ Health monitor is running"
            echo ""
            echo "Recent health monitor logs:"
            pm2 logs health-monitor --lines 10 --nostream
        else
            echo "❌ Health monitor is not running"
            echo "Start it with: pm2 start health-check.js --name health-monitor"
        fi
        ;;
    setup)
        echo "🔧 Setting up PM2 startup..."
        "$SCRIPT_DIR/setup-pm2-startup.sh"
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
