#!/bin/bash

# 📊 Perplexica PM2 Monitoring Script

echo "📊 Perplexica Services Monitoring Dashboard"
echo "=========================================="

# Function to check service health
check_service_health() {
    local url=$1
    local service_name=$2
    
    if curl -s "$url" >/dev/null 2>&1; then
        echo "✅ $service_name: HEALTHY"
    else
        echo "❌ $service_name: UNHEALTHY"
    fi
}

# 1. PM2 Status
echo ""
echo "🔄 PM2 Process Status:"
pm2 status

# 2. Service Health Checks
echo ""
echo "🏥 Service Health Checks:"
check_service_health "http://localhost:3001" "Perplexica App"
check_service_health "http://localhost:8090" "Auth Proxy"
check_service_health "http://127.0.0.1:3004/health" "MCP Server"

# 3. Docker Status
echo ""
echo "🐳 Docker Container Status:"
cd /home/<USER>/Perplexica
docker compose ps

# 4. Resource Usage
echo ""
echo "💻 Resource Usage:"
pm2 monit --no-interaction | head -20

# 5. Recent Logs
echo ""
echo "📄 Recent Logs (last 10 lines):"
echo "--- MCP Server ---"
pm2 logs perplexica-mcp-server --lines 5 --nostream 2>/dev/null || echo "No logs available"

echo ""
echo "--- Auth Proxy ---"
pm2 logs auth-proxy --lines 5 --nostream 2>/dev/null || echo "No logs available"

# 6. System Info
echo ""
echo "🖥️  System Info:"
echo "   Uptime: $(uptime -p)"
echo "   Load: $(uptime | awk -F'load average:' '{print $2}')"
echo "   Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "   Disk: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5" used)"}')"

echo ""
echo "🔧 Management Commands:"
echo "   🚀 Start: ./start-perplexica-pm2.sh"
echo "   🛑 Stop: ./stop-perplexica-pm2.sh"
echo "   🔄 Restart: ./restart-perplexica-pm2.sh"
echo "   📈 Live Monitor: pm2 monit"
echo "   📄 Live Logs: pm2 logs"
echo "   🔍 Detailed Status: pm2 show <app-name>"
