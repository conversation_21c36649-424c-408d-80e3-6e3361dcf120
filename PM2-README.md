# 🚀 Perplexica PM2 Management

Hệ thống quản lý Perplexica với PM2 để tự động restart khi crash và monitoring.

## 📋 Tổng quan

PM2 sẽ quản lý các services sau:
- **perplexica-mcp-server**: MCP Server chính
- **auth-proxy**: Authentication Proxy 
- **health-monitor**: Health monitoring và auto-healing

## 🚀 Cách sử dụng

### <PERSON><PERSON><PERSON> ch<PERSON>h (Khuyến nghị)
```bash
# Hiển thị help
./perplexica-pm2.sh help

# Khởi động tất cả services
./perplexica-pm2.sh start

# Dừng tất cả services  
./perplexica-pm2.sh stop

# Restart tất cả services
./perplexica-pm2.sh restart

# Xem status
./perplexica-pm2.sh status

# Monitoring dashboard
./perplexica-pm2.sh monitor

# Xem logs live
./perplexica-pm2.sh logs

# Kiểm tra health
./perplexica-pm2.sh health
```

### Scripts riêng lẻ
```bash
# Khởi động
./start-perplexica-pm2.sh

# Dừng
./stop-perplexica-pm2.sh

# Restart
./restart-perplexica-pm2.sh

# Monitoring
./monitor-perplexica.sh
```

## 🔧 Setup lần đầu

1. **Chạy setup startup** (chỉ cần 1 lần):
```bash
./setup-pm2-startup.sh
```

2. **Chạy lệnh sudo được hiển thị** để enable PM2 startup

3. **Test khởi động**:
```bash
./perplexica-pm2.sh start
```

## 📊 Monitoring & Logs

### PM2 Commands
```bash
# Xem status tất cả processes
pm2 status

# Monitor real-time
pm2 monit

# Xem logs
pm2 logs

# Xem logs của service cụ thể
pm2 logs perplexica-mcp-server
pm2 logs auth-proxy
pm2 logs health-monitor

# Restart service cụ thể
pm2 restart perplexica-mcp-server

# Stop service cụ thể
pm2 stop auth-proxy

# Xem thông tin chi tiết
pm2 show perplexica-mcp-server
```

### Log Files
Logs được lưu trong thư mục `logs/`:
- `mcp-server-*.log`: MCP Server logs
- `auth-proxy-*.log`: Auth Proxy logs  
- `health-monitor-*.log`: Health Monitor logs

## 🏥 Health Monitoring

Health monitor tự động:
- Kiểm tra health của các services mỗi 30 giây
- Tự động restart service khi fail 3 lần liên tiếp
- Ghi log chi tiết về trạng thái services

Services được monitor:
- **Perplexica App** (http://localhost:3001) - Critical
- **Auth Proxy** (http://localhost:8090) - Critical  
- **MCP Server** (http://127.0.0.1:3004/health) - Non-critical

## 🔄 Auto Restart

PM2 được cấu hình để:
- Tự động restart khi process crash
- Restart tối đa 10 lần với delay tăng dần
- Restart khi memory usage > 1GB (MCP Server)
- Restart khi memory usage > 500MB (Auth Proxy)

## 🚨 Troubleshooting

### Service không start
```bash
# Kiểm tra logs
pm2 logs

# Kiểm tra status
pm2 status

# Restart thủ công
pm2 restart all
```

### Docker containers không chạy
```bash
# Kiểm tra Docker
cd /home/<USER>/Perplexica
docker compose ps

# Restart Docker containers
docker compose restart
```

### Health monitor báo lỗi
```bash
# Xem logs health monitor
pm2 logs health-monitor

# Restart health monitor
pm2 restart health-monitor
```

### Reset hoàn toàn
```bash
# Dừng tất cả
./perplexica-pm2.sh stop

# Xóa PM2 processes
pm2 delete all

# Khởi động lại
./perplexica-pm2.sh start
```

## 📁 File Structure

```
/home/<USER>/perplexica/
├── ecosystem.config.js          # PM2 configuration
├── health-check.js              # Health monitoring script
├── perplexica-pm2.sh           # Main management script
├── start-perplexica-pm2.sh     # Start script
├── stop-perplexica-pm2.sh      # Stop script  
├── restart-perplexica-pm2.sh   # Restart script
├── monitor-perplexica.sh       # Monitoring script
├── setup-pm2-startup.sh        # Setup script
└── logs/                       # Log directory
    ├── mcp-server-*.log
    ├── auth-proxy-*.log
    └── health-monitor-*.log
```

## 🔑 Service URLs

- **Auth Proxy**: http://localhost:8090 (Username: tomkho12, Password: Twilv0zera@123)
- **MCP Server**: http://127.0.0.1:3004
- **Perplexica App**: http://localhost:3001 (internal)

## 💡 Tips

1. **Luôn sử dụng script chính**: `./perplexica-pm2.sh [command]`
2. **Monitor thường xuyên**: `./perplexica-pm2.sh monitor`
3. **Kiểm tra logs khi có vấn đề**: `pm2 logs`
4. **Backup PM2 config**: `pm2 save` sau khi thay đổi
5. **Test sau khi reboot**: Đảm bảo auto-startup hoạt động
