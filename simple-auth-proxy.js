const http = require('http');
const httpProxy = require('http-proxy');

// Tạo proxy server
const proxy = httpProxy.createProxyServer({});

// C<PERSON>u hình auth
const AUTH_USER = 'tomkho12';
const AUTH_PASS = 'Twilv0zera@123';
const TARGET_URL = 'http://localhost:3002';
const PROXY_PORT = 8090;

// Hàm kiểm tra Basic Auth
function checkAuth(req) {
  const auth = req.headers.authorization;
  if (!auth || !auth.startsWith('Basic ')) {
    return false;
  }
  
  const credentials = Buffer.from(auth.slice(6), 'base64').toString();
  const [username, password] = credentials.split(':');
  
  return username === AUTH_USER && password === AUTH_PASS;
}

// Tạo server
const server = http.createServer((req, res) => {
  // <PERSON><PERSON>m tra auth
  if (!checkAuth(req)) {
    res.writeHead(401, {
      'WWW-Authenticate': 'Basic realm="Perplexica"',
      'Content-Type': 'text/plain'
    });
    res.end('Authentication required');
    return;
  }

  // Proxy request đến target
  proxy.web(req, res, {
    target: TARGET_URL,
    changeOrigin: true
  });
});

// Handle proxy errors
proxy.on('error', (err, req, res) => {
  console.error('Proxy error:', err);
  if (!res.headersSent) {
    res.writeHead(500, { 'Content-Type': 'text/plain' });
    res.end('Proxy error');
  }
});

// Handle WebSocket upgrades
server.on('upgrade', (req, socket, head) => {
  if (!checkAuth(req)) {
    socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n');
    socket.destroy();
    return;
  }

  proxy.ws(req, socket, head, {
    target: TARGET_URL,
    changeOrigin: true
  });
});

server.listen(PROXY_PORT, () => {
  console.log(`🔐 Auth proxy running on http://localhost:${PROXY_PORT}`);
  console.log(`📡 Proxying to ${TARGET_URL}`);
  console.log(`👤 Username: ${AUTH_USER}`);
  console.log(`🔑 Password: ${AUTH_PASS}`);
});
