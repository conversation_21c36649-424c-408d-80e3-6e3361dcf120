#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🧪 Testing Perplexica MCP Server');
console.log('================================');

// Test MCP server by sending JSON-RPC requests
const mcpServer = spawn('node', [join(__dirname, 'index.js')], {
  stdio: ['pipe', 'pipe', 'pipe'],
  env: {
    ...process.env,
    PERPLEXICA_API_URL: 'http://localhost:3001'
  }
});

let responseBuffer = '';

mcpServer.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // Try to parse complete JSON-RPC responses
  const lines = responseBuffer.split('\n');
  responseBuffer = lines.pop() || ''; // Keep incomplete line
  
  lines.forEach(line => {
    if (line.trim()) {
      try {
        const response = JSON.parse(line);
        console.log('📨 Response:', JSON.stringify(response, null, 2));
      } catch (e) {
        console.log('📝 Output:', line);
      }
    }
  });
});

mcpServer.stderr.on('data', (data) => {
  console.log('🔍 Server:', data.toString().trim());
});

mcpServer.on('close', (code) => {
  console.log(`\n🏁 MCP server exited with code ${code}`);
  process.exit(code);
});

// Send test requests
const sendRequest = (request) => {
  const jsonRequest = JSON.stringify(request) + '\n';
  console.log('📤 Sending:', JSON.stringify(request, null, 2));
  mcpServer.stdin.write(jsonRequest);
};

// Wait a bit for server to start
setTimeout(() => {
  console.log('\n🚀 Starting tests...\n');
  
  // Test 1: List tools
  sendRequest({
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list',
    params: {}
  });
  
  // Test 2: List resources
  setTimeout(() => {
    sendRequest({
      jsonrpc: '2.0',
      id: 2,
      method: 'resources/list',
      params: {}
    });
  }, 1000);
  
  // Test 3: List prompts
  setTimeout(() => {
    sendRequest({
      jsonrpc: '2.0',
      id: 3,
      method: 'prompts/list',
      params: {}
    });
  }, 2000);
  
  // Test 4: Call search tool
  setTimeout(() => {
    sendRequest({
      jsonrpc: '2.0',
      id: 4,
      method: 'tools/call',
      params: {
        name: 'perplexica_search',
        arguments: {
          query: 'What is artificial intelligence?',
          focusMode: 'webSearch'
        }
      }
    });
  }, 3000);
  
  // Close after tests
  setTimeout(() => {
    mcpServer.kill();
  }, 10000);
  
}, 1000);
