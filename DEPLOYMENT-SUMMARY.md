# 🚀 Perplexica PM2 Deployment - <PERSON><PERSON><PERSON> thành!

## ✅ Đã triển khai thành công

Hệ thống PM2 cho Perplexica đã được triển khai hoàn chỉnh với các tính năng:

### 🔧 Services được quản lý
- **perplexica-mcp-server**: MCP Server chính (Port: 3004)
- **auth-proxy**: Authentication Proxy (Port: 8090) 
- **health-monitor**: Health monitoring và auto-healing
- **Docker containers**: Perplexica App (Port: 3001) + SearXNG (Port: 4000)

### 🛡️ Tính năng bảo vệ
- ✅ **Auto-restart khi crash**: PM2 tự động restart process khi bị lỗi
- ✅ **Memory limit protection**: Restart khi memory usage quá cao
- ✅ **Health monitoring**: Kiểm tra health mỗi 30 giây
- ✅ **Auto-healing**: Tự động restart service khi fail 3 lần liên tiếp
- ✅ **Exponential backoff**: Delay tăng dần khi restart liên tục
- ✅ **Startup on boot**: Tự động khởi động khi server reboot

## 🎯 Cách sử dụng

### Script chính (Khuyến nghị)
```bash
# Xem tất cả lệnh
./perplexica-pm2.sh help

# Khởi động tất cả services
./perplexica-pm2.sh start

# Xem status
./perplexica-pm2.sh status

# Monitoring dashboard
./perplexica-pm2.sh monitor

# Restart tất cả
./perplexica-pm2.sh restart

# Dừng tất cả
./perplexica-pm2.sh stop
```

### PM2 Commands trực tiếp
```bash
# Xem status
pm2 status

# Monitor real-time
pm2 monit

# Xem logs
pm2 logs

# Restart service cụ thể
pm2 restart perplexica-mcp-server
```

## 📊 Monitoring

### Health Check
- **Perplexica App**: http://localhost:3001 ✅
- **Auth Proxy**: http://localhost:8090 ✅ 
- **MCP Server**: Chạy qua stdio (không có HTTP endpoint)

### Logs Location
```
/home/<USER>/perplexica/logs/
├── mcp-server-*.log
├── auth-proxy-*.log
└── health-monitor-*.log
```

## 🔑 Service URLs

- **Public Access**: http://localhost:8090 (Auth: tomkho12/Twilv0zera@123)
- **MCP Server**: http://127.0.0.1:3004 (Local only)
- **Perplexica App**: http://localhost:3001 (Local only)

## 🚨 Troubleshooting

### Service không chạy
```bash
# Kiểm tra status
./perplexica-pm2.sh status

# Xem logs
pm2 logs

# Restart
./perplexica-pm2.sh restart
```

### Reset hoàn toàn
```bash
# Dừng tất cả
./perplexica-pm2.sh stop

# Xóa PM2 processes
pm2 delete all

# Khởi động lại
./perplexica-pm2.sh start
```

## 📁 Files được tạo

```
/home/<USER>/perplexica/
├── ecosystem.config.cjs         # PM2 configuration
├── health-check.js              # Health monitoring
├── perplexica-pm2.sh           # Main management script ⭐
├── start-perplexica-pm2.sh     # Start script
├── stop-perplexica-pm2.sh      # Stop script
├── restart-perplexica-pm2.sh   # Restart script
├── monitor-perplexica.sh       # Monitoring script
├── setup-pm2-startup.sh        # Setup startup (run once)
├── PM2-README.md               # Detailed documentation
└── logs/                       # Log directory
```

## 🎉 Kết quả

✅ **Không còn bị tắt đột ngột**: PM2 sẽ tự động restart khi crash
✅ **Monitoring 24/7**: Health monitor kiểm tra liên tục
✅ **Easy management**: Script đơn giản để quản lý
✅ **Auto startup**: Tự động khởi động khi server reboot
✅ **Detailed logs**: Logs chi tiết cho debugging

## 🔄 Next Steps

1. **Setup startup** (chỉ cần 1 lần):
```bash
./setup-pm2-startup.sh
# Sau đó chạy lệnh sudo được hiển thị
```

2. **Test reboot**: 
```bash
sudo reboot
# Kiểm tra sau khi reboot: ./perplexica-pm2.sh status
```

3. **Monitor thường xuyên**:
```bash
./perplexica-pm2.sh monitor
```

---

🎯 **Perplexica giờ đây sẽ chạy ổn định và tự động phục hồi khi có sự cố!**
